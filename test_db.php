<?php
echo "Testing database connection...\n";

$servername = "localhost";
$username = "u111133901_test_alm";
$password = "<PERSON><PERSON><PERSON>@0505";
$database = "u111133901_test_alm";

// Create connection
$conn = new mysqli($servername, $username, $password, $database);

// Check connection
if ($conn->connect_error) {
    echo "❌ Database connection failed: " . $conn->connect_error . "\n";
    echo "\nPlease check:\n";
    echo "1. MySQL server is running\n";
    echo "2. Database credentials are correct\n";
    echo "3. Database exists\n";
    echo "4. User has proper permissions\n";
} else {
    echo "✅ Database connection successful!\n";
    echo "Connected to database: " . $database . "\n";
    
    // Test query
    $result = $conn->query("SELECT VERSION() as version");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "MySQL Version: " . $row['version'] . "\n";
    }
}

$conn->close();
?>

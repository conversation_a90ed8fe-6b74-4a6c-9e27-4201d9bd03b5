<?php
// Registration is disabled for public access
// Only administrators can access this page
session_start();

// Check if user is already logged in (admin creating accounts for others)
$isLoggedIn = isset($_SESSION['user_logged_in']) && $_SESSION['user_logged_in'] === true;

// For now, completely disable public registration
if (!$isLoggedIn) {
    header("Location: login.php?message=registration_disabled");
    exit;
}

$pageTitle = "Register New User - Business Finder";

// Handle registration form submission
$registerError = '';
$registerSuccess = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = trim($_POST['password'] ?? '');
    $confirmPassword = trim($_POST['confirm_password'] ?? '');
    
    // Validation
    if (empty($username) || empty($email) || empty($password) || empty($confirmPassword)) {
        $registerError = 'All fields are required.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $registerError = 'Please enter a valid email address.';
    } elseif (strlen($password) < 6) {
        $registerError = 'Password must be at least 6 characters long.';
    } elseif ($password !== $confirmPassword) {
        $registerError = 'Passwords do not match.';
    } else {
        // Load existing users
        $usersFile = 'users.json';
        $users = [];
        
        if (file_exists($usersFile)) {
            $usersData = file_get_contents($usersFile);
            $users = json_decode($usersData, true) ?? [];
        }
        
        // Check if username or email already exists
        $userExists = false;
        foreach ($users as $user) {
            if ($user['username'] === $username || $user['email'] === $email) {
                $userExists = true;
                break;
            }
        }
        
        if ($userExists) {
            $registerError = 'Username or email already exists.';
        } else {
            // Create new user
            $newUser = [
                'id' => uniqid(),
                'username' => $username,
                'email' => $email,
                'password' => password_hash($password, PASSWORD_DEFAULT),
                'created_at' => date('Y-m-d H:i:s'),
                'last_login' => null,
                'status' => 'active'
            ];
            
            $users[] = $newUser;
            
            // Save to JSON file
            if (file_put_contents($usersFile, json_encode($users, JSON_PRETTY_PRINT))) {
                $registerSuccess = 'Registration successful! You can now login.';
                // Clear form data
                $_POST = [];
            } else {
                $registerError = 'Failed to save user data. Please try again.';
            }
        }
    }
}

include 'header.php';
?>

<style>
    .register-container {
        max-width: 450px;
        margin: 50px auto;
        background: white;
        padding: 40px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .register-header {
        text-align: center;
        margin-bottom: 30px;
    }
    
    .register-header h2 {
        color: #333;
        margin-bottom: 10px;
    }
    
    .register-header p {
        color: #666;
        font-size: 14px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        color: #333;
        font-weight: 500;
    }
    
    .form-group input {
        width: 100%;
        padding: 12px;
        border: 2px solid #ddd;
        border-radius: 5px;
        font-size: 16px;
        transition: border-color 0.3s;
    }
    
    .form-group input:focus {
        outline: none;
        border-color: #667eea;
    }
    
    .btn-register {
        width: 100%;
        padding: 12px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 5px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: transform 0.2s;
    }
    
    .btn-register:hover {
        transform: translateY(-2px);
    }
    
    .alert {
        padding: 12px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .alert-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .login-link {
        text-align: center;
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }
    
    .login-link a {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
    }
    
    .login-link a:hover {
        text-decoration: underline;
    }
    
    .password-requirements {
        font-size: 12px;
        color: #6c757d;
        margin-top: 5px;
    }
</style>

<div class="register-container">
    <div class="register-header">
        <h2>👥 Create New User Account</h2>
        <p>Administrator: Create a new user account for the system</p>
    </div>
    
    <?php if ($registerError): ?>
        <div class="alert alert-error">
            <?= htmlspecialchars($registerError) ?>
        </div>
    <?php endif; ?>
    
    <?php if ($registerSuccess): ?>
        <div class="alert alert-success">
            <?= htmlspecialchars($registerSuccess) ?>
            <br><a href="login.php" style="color: #155724; font-weight: bold;">Click here to login</a>
        </div>
    <?php endif; ?>
    
    <form method="POST" action="">
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" required 
                   value="<?= htmlspecialchars($_POST['username'] ?? '') ?>"
                   placeholder="Choose a username">
        </div>
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required 
                   value="<?= htmlspecialchars($_POST['email'] ?? '') ?>"
                   placeholder="Enter your email address">
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required 
                   placeholder="Create a password">
            <div class="password-requirements">
                Password must be at least 6 characters long
            </div>
        </div>
        
        <div class="form-group">
            <label for="confirm_password">Confirm Password:</label>
            <input type="password" id="confirm_password" name="confirm_password" required 
                   placeholder="Confirm your password">
        </div>
        
        <button type="submit" class="btn-register">Register</button>
    </form>
    
    <div class="login-link">
        <p>Already have an account? <a href="login.php">Login here</a></p>
    </div>
</div>

<?php include 'footer.php'; ?>

# Database Setup for Business Search Application

## Overview
The application now saves all search results and search history to a MySQL database for analytics and data persistence.

## Database Tables

### 1. `search_history`
Stores information about each search performed:
- `search_id`: Unique identifier for each search
- `pincode`: The pincode searched
- `keywords`: Keywords used (if any)
- `total_results`: Number of results found
- `search_timestamp`: When the search was performed
- `ip_address`: User's IP address
- `user_agent`: User's browser information

### 2. `business_search_results`
Stores detailed information about each business found:
- `search_id`: Links to the search that found this business
- `business_name`: Name of the business
- `business_address`: Full address
- `business_rating`: Google rating (if available)
- `business_phone`: Phone number (if available)
- `business_types`: Business categories
- `place_id`: Google Places unique identifier
- `latitude`, `longitude`: Geographic coordinates

## Setup Instructions

### Method 1: Automatic Setup (if database connection works)
1. Run: `php setup_database.php`
2. This will create both tables automatically

### Method 2: Manual Setup (recommended)
1. Open your MySQL database management tool (phpMyAdmin, MySQL Workbench, etc.)
2. Connect to database: `u111133901_test_alm`
3. Run the SQL commands from `manual_setup.sql`

### Method 3: Command Line
```sql
mysql -u u111133901_test_alm -p u111133901_test_alm < manual_setup.sql
```

## Features

### Application Features
- ✅ **Graceful Degradation**: App works even if database is not connected
- ✅ **Search History**: All searches are logged with timestamps
- ✅ **Duplicate Prevention**: Same business won't be saved multiple times
- ✅ **Error Handling**: Database errors don't break the search functionality
- ✅ **Status Indicator**: Shows whether data was saved to database

### Admin Panel Features
- 📊 **Statistics Dashboard**: View search statistics and trends
- 🔍 **Search History**: Browse all previous searches
- 📈 **Analytics**: Top pincodes, average results, etc.
- 🔗 **Detailed Views**: Click to see full results for any search

## Usage

### For Users
1. Use the search form normally - database saving is automatic
2. Look for "Data Status" in results to confirm database saving
3. Click "View Admin Dashboard" to see analytics (if database connected)

### For Administrators
1. Visit `/admin.php` to see the admin dashboard
2. View statistics, recent searches, and top pincodes
3. Click on any search ID to see detailed results
4. Use the data for business intelligence and trend analysis

## Database Connection Status

The application will show:
- ✅ **"Saved to Database"** - Database is connected and data is saved
- ⚠️ **"Database not connected"** - App works but data is not saved

## Files Created

- `create_table.sql` - SQL commands for table creation
- `manual_setup.sql` - Complete manual setup with verification
- `setup_database.php` - Automatic setup script
- `test_db.php` - Database connection test
- `admin.php` - Admin dashboard for viewing saved data
- `DATABASE_SETUP.md` - This documentation

## Troubleshooting

### Database Connection Issues
1. Check if MySQL server is running
2. Verify database credentials in `db_connect.php`
3. Ensure database `u111133901_test_alm` exists
4. Check user permissions
5. Run `php test_db.php` to diagnose connection issues

### Common Solutions
- **Connection refused**: MySQL server not running
- **Access denied**: Wrong username/password
- **Database doesn't exist**: Create the database first
- **Table doesn't exist**: Run the setup SQL commands

## Data Privacy
- IP addresses and user agents are stored for analytics
- No personal information is collected beyond search terms
- Data can be used for improving search functionality and business insights

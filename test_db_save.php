<?php
// Test database connection and saving functionality
echo "<h2>Database Connection & Save Test</h2>";

// Test 1: Database Connection
echo "<h3>1. Testing Database Connection</h3>";
try {
    require 'db_connect.php';
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    echo "<p>Connected to database: " . $conn->server_info . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    exit;
}

// Test 2: Check if table exists
echo "<h3>2. Checking Table Structure</h3>";
$result = $conn->query("SHOW TABLES LIKE 'business_search_results'");
if ($result->num_rows > 0) {
    echo "<p style='color: green;'>✅ Table 'business_search_results' exists</p>";
    
    // Show table structure
    $structure = $conn->query("DESCRIBE business_search_results");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
    while($row = $structure->fetch_assoc()) {
        echo "<tr><td>{$row['Field']}</td><td>{$row['Type']}</td><td>{$row['Null']}</td><td>{$row['Key']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ Table 'business_search_results' does not exist</p>";
    exit;
}

// Test 3: Test Insert
echo "<h3>3. Testing Data Insert</h3>";
$testData = [
    'search_id' => 'test_' . date('Ymd_His'),
    'pincode' => '110001',
    'keywords' => 'test restaurant',
    'business_name' => 'Test Restaurant',
    'business_address' => 'Test Address, Delhi',
    'business_rating' => 4.5,
    'business_phone' => '+91 9876543210',
    'business_types' => 'restaurant, food',
    'place_id' => 'test_place_' . time(),
    'latitude' => 28.6139,
    'longitude' => 77.2090
];

$stmt = $conn->prepare("INSERT INTO business_search_results (search_id, pincode, keywords, business_name, business_address, business_rating, business_phone, business_types, place_id, latitude, longitude) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

if ($stmt) {
    $stmt->bind_param("sssssssssdd", 
        $testData['search_id'],
        $testData['pincode'],
        $testData['keywords'],
        $testData['business_name'],
        $testData['business_address'],
        $testData['business_rating'],
        $testData['business_phone'],
        $testData['business_types'],
        $testData['place_id'],
        $testData['latitude'],
        $testData['longitude']
    );
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ Test data inserted successfully!</p>";
        echo "<p>Inserted record with search_id: " . $testData['search_id'] . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Insert failed: " . $stmt->error . "</p>";
    }
    $stmt->close();
} else {
    echo "<p style='color: red;'>❌ Prepare statement failed: " . $conn->error . "</p>";
}

// Test 4: Check if data was saved
echo "<h3>4. Verifying Saved Data</h3>";
$result = $conn->query("SELECT COUNT(*) as count FROM business_search_results");
$row = $result->fetch_assoc();
echo "<p>Total records in table: <strong>" . $row['count'] . "</strong></p>";

if ($row['count'] > 0) {
    echo "<h4>Recent Records:</h4>";
    $recent = $conn->query("SELECT * FROM business_search_results ORDER BY created_at DESC LIMIT 5");
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Search ID</th><th>Pincode</th><th>Business Name</th><th>Created At</th></tr>";
    while($row = $recent->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['search_id'] . "</td>";
        echo "<td>" . $row['pincode'] . "</td>";
        echo "<td>" . $row['business_name'] . "</td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Test 5: Check the saveBusinessResult function
echo "<h3>5. Testing saveBusinessResult Function</h3>";

function saveBusinessResult($conn, $searchId, $pincode, $keywords, $businessData, $lat, $lng) {
    $stmt = $conn->prepare("INSERT INTO business_search_results (search_id, pincode, keywords, business_name, business_address, business_rating, business_phone, business_types, place_id, latitude, longitude) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE search_id = VALUES(search_id), updated_at = CURRENT_TIMESTAMP");
    
    $rating = ($businessData['rating'] !== 'N/A') ? $businessData['rating'] : null;
    $phone = ($businessData['phone'] !== 'N/A') ? $businessData['phone'] : null;
    
    $stmt->bind_param("sssssssssdd", 
        $searchId, 
        $pincode, 
        $keywords, 
        $businessData['name'], 
        $businessData['address'], 
        $rating, 
        $phone, 
        $businessData['types'], 
        $businessData['place_id'], 
        $lat, 
        $lng
    );
    
    return $stmt->execute();
}

// Test the function
$testBusinessData = [
    'name' => 'Function Test Business',
    'address' => 'Function Test Address',
    'rating' => 'N/A',
    'phone' => 'N/A',
    'types' => 'test, business',
    'place_id' => 'function_test_' . time()
];

try {
    $result = saveBusinessResult($conn, 'function_test_' . time(), '110001', 'test', $testBusinessData, 28.6139, 77.2090);
    if ($result) {
        echo "<p style='color: green;'>✅ saveBusinessResult function works correctly!</p>";
    } else {
        echo "<p style='color: red;'>❌ saveBusinessResult function failed</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Function error: " . $e->getMessage() . "</p>";
}

$conn->close();
?>

<style>
body { font-family: Arial, sans-serif; padding: 20px; max-width: 1000px; margin: 0 auto; }
table { width: 100%; margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
h3 { color: #333; border-bottom: 2px solid #667eea; padding-bottom: 5px; }
</style>

<?php
// Verify user B<PERSON>999 exists and can login with Welcome@1

$username = 'BM999';
$password = 'Welcome@1';

echo "<h2>🔍 User Verification</h2>";

// Check if users.json exists
if (!file_exists('users.json')) {
    echo "<p>❌ users.json file not found. Creating user...</p>";
    
    // Create the user
    $newUser = [
        'id' => 'user_bm999_001',
        'username' => $username,
        'email' => '<EMAIL>',
        'password' => password_hash($password, PASSWORD_DEFAULT),
        'created_at' => date('Y-m-d H:i:s'),
        'last_login' => null,
        'status' => 'active'
    ];
    
    $users = [$newUser];
    
    if (file_put_contents('users.json', json_encode($users, JSON_PRETTY_PRINT))) {
        echo "<p>✅ User created successfully!</p>";
    } else {
        echo "<p>❌ Failed to create user file</p>";
        exit;
    }
} else {
    echo "<p>✅ users.json file exists</p>";
}

// Load users
$usersData = file_get_contents('users.json');
$users = json_decode($usersData, true);

if (!$users) {
    echo "<p>❌ Failed to parse users.json</p>";
    exit;
}

echo "<p>📊 Total users in system: " . count($users) . "</p>";

// Find the user
$userFound = false;
foreach ($users as $user) {
    if ($user['username'] === $username) {
        $userFound = true;
        echo "<p>✅ User '$username' found in system</p>";
        echo "<p><strong>User Details:</strong></p>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> " . $user['id'] . "</li>";
        echo "<li><strong>Username:</strong> " . $user['username'] . "</li>";
        echo "<li><strong>Email:</strong> " . $user['email'] . "</li>";
        echo "<li><strong>Created:</strong> " . $user['created_at'] . "</li>";
        echo "<li><strong>Status:</strong> " . $user['status'] . "</li>";
        echo "</ul>";
        
        // Test password
        if (password_verify($password, $user['password'])) {
            echo "<p>✅ Password verification successful!</p>";
            echo "<p><strong>Login Credentials:</strong></p>";
            echo "<p><strong>Username:</strong> $username</p>";
            echo "<p><strong>Password:</strong> $password</p>";
        } else {
            echo "<p>❌ Password verification failed!</p>";
        }
        break;
    }
}

if (!$userFound) {
    echo "<p>❌ User '$username' not found</p>";
}

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li><a href='login.php'>Test Login</a></li>";
echo "<li><a href='index.php'>Go to Home Page</a></li>";
echo "<li>Delete this verification file for security</li>";
echo "</ol>";
?>

<style>
body { font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; }
h2 { color: #333; }
p { line-height: 1.6; }
ul, ol { line-height: 1.8; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>

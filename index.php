<?php
session_start();
$pageTitle = "Home - Business Finder";
$isLoggedIn = isset($_SESSION['user_logged_in']) && $_SESSION['user_logged_in'] === true;
include 'header.php';
?>

<style>
    .hero-section {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        padding: 60px 0;
        text-align: center;
        border-radius: 15px;
        margin-bottom: 40px;
    }

    .hero-section h1 {
        font-size: 2.5em;
        color: #333;
        margin-bottom: 20px;
    }

    .hero-section p {
        font-size: 1.2em;
        color: #666;
        margin-bottom: 30px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .search-container {
        max-width: 600px;
        margin: 0 auto;
        background: white;
        padding: 40px;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    }

    .search-form h2 {
        text-align: center;
        color: #333;
        margin-bottom: 30px;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #333;
        font-weight: 500;
        font-size: 16px;
    }

    .form-group input {
        width: 100%;
        padding: 15px;
        border: 2px solid #ddd;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.3s;
    }

    .form-group input:focus {
        outline: none;
        border-color: #667eea;
    }

    .form-group small {
        display: block;
        color: #666;
        margin-top: 8px;
        font-size: 14px;
    }

    .search-button {
        width: 100%;
        padding: 15px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 18px;
        font-weight: 500;
        cursor: pointer;
        transition: transform 0.2s;
    }

    .search-button:hover {
        transform: translateY(-2px);
    }

    .features-section {
        margin-top: 50px;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 30px;
    }

    .feature-card {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        text-align: center;
    }

    .feature-card .icon {
        font-size: 48px;
        margin-bottom: 20px;
        display: block;
    }

    .feature-card h3 {
        color: #333;
        margin-bottom: 15px;
    }

    .feature-card p {
        color: #666;
        line-height: 1.6;
    }
</style>

<?php if ($isLoggedIn): ?>
    <!-- Logged-in User Content -->
    <div class="hero-section">
        <h1>Welcome back, <?= htmlspecialchars($_SESSION['username']) ?>! 👋</h1>
        <p>Ready to find businesses without websites? Access your dashboard and start searching for potential clients.</p>
    </div>

    <div class="dashboard-grid">
        <div class="feature-card">
            <span class="icon">📊</span>
            <h3>Dashboard</h3>
            <p>View your search statistics, recent activity, and account overview.</p>
            <a href="dashboard.php" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-decoration: none; border-radius: 8px; font-weight: 500; margin-top: 15px;">Go to Dashboard</a>
        </div>

        <div class="feature-card">
            <span class="icon">🔍</span>
            <h3>Search Businesses</h3>
            <p>Find businesses without websites using pincode and keyword combinations.</p>
            <a href="search.php" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-decoration: none; border-radius: 8px; font-weight: 500; margin-top: 15px;">Start Searching</a>
        </div>

        <div class="feature-card">
            <span class="icon">📋</span>
            <h3>View Results</h3>
            <p>Browse and manage all your saved search results from the database.</p>
            <a href="view-results.php" style="display: inline-block; padding: 12px 24px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-decoration: none; border-radius: 8px; font-weight: 500; margin-top: 15px;">View Results</a>
        </div>
    </div>

<?php else: ?>
    <!-- Public User Content -->
    <div class="hero-section">
        <h1>🔍 Business Finder</h1>
        <p>Professional tool for finding businesses without websites. Perfect for digital marketing agencies, web developers, and business consultants.</p>
    </div>

    <div class="login-prompt" style="background: white; padding: 40px; border-radius: 15px; box-shadow: 0 5px 20px rgba(0,0,0,0.1); text-align: center; margin-bottom: 40px;">
        <h2 style="color: #333; margin-bottom: 20px;">🔐 Login Required</h2>
        <p style="color: #666; margin-bottom: 30px; font-size: 16px;">
            Access to search functionality requires a user account. Please login or register to start finding businesses without websites.
        </p>
        <div style="text-align: center;">
            <a href="login.php" style="display: inline-block; padding: 15px 30px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-decoration: none; border-radius: 8px; font-weight: 500;">🔑 Login</a>
        </div>
    </div>
<?php endif; ?>

<div class="features-section">
    <div class="feature-card">
        <span class="icon">🎯</span>
        <h3>Targeted Search</h3>
        <p>Search by specific location (pincode) and business type (keywords) to find exactly what you're looking for.</p>
    </div>

    <div class="feature-card">
        <span class="icon">⚡</span>
        <h3>Real-time Results</h3>
        <p>Get up-to-date business information directly from Google Places API with accurate contact details.</p>
    </div>

    <div class="feature-card">
        <span class="icon">💼</span>
        <h3>Business Opportunities</h3>
        <p>Find businesses without websites - perfect prospects for web development and digital marketing services.</p>
    </div>
</div>

<?php include 'footer.php'; ?>

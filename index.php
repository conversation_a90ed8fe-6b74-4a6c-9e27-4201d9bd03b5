<!DOCTYPE html>
<html>
<head>
    <title>Find Businesses by <PERSON>ncode</title>
    <style>
        body { font-family: Arial; padding: 20px; }
        input, button { padding: 8px; font-size: 16px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { padding: 10px; border: 1px solid #ccc; }
        th { background: #f0f0f0; }
    </style>
</head>
<body>

<h2>Find Businesses Without Website (by Pincode & Keywords)</h2>

<form method="POST" action="fetch.php">
    <div style="margin-bottom: 15px;">
        <label for="pincode">Enter Pincode:</label><br>
        <input type="text" name="pincode" id="pincode" required pattern="\d{6}" placeholder="e.g., 110001">
    </div>

    <div style="margin-bottom: 15px;">
        <label for="keywords">Enter Keywords (optional):</label><br>
        <input type="text" name="keywords" id="keywords" placeholder="e.g., restaurant, shop, clinic" style="width: 300px;">
        <small style="display: block; color: #666; margin-top: 5px;">
            Separate multiple keywords with commas. Leave empty to search all business types.
        </small>
    </div>

    <button type="submit">Search</button>
</form>

</body>
</html>

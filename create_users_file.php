<?php
// Create users.json file with user BM999

echo "<h2>Creating users.json file...</h2>";

// User data
$username = 'BM999';
$password = 'Welcome@1';
$email = '<EMAIL>';

// Generate proper password hash
$hashedPassword = password_hash($password, PASSWORD_DEFAULT);

// Create user array
$user = [
    'id' => 'user_bm999_' . time(),
    'username' => $username,
    'email' => $email,
    'password' => $hashedPassword,
    'created_at' => date('Y-m-d H:i:s'),
    'last_login' => null,
    'status' => 'active'
];

// Create users array
$users = [$user];

// Convert to JSON
$jsonData = json_encode($users, JSON_PRETTY_PRINT);

// Write to file
$filename = 'users.json';
$result = file_put_contents($filename, $jsonData);

if ($result !== false) {
    echo "<p style='color: green;'>✅ users.json created successfully!</p>";
    echo "<p><strong>File size:</strong> " . filesize($filename) . " bytes</p>";
    echo "<p><strong>File permissions:</strong> " . substr(sprintf('%o', fileperms($filename)), -4) . "</p>";
    
    echo "<h3>User Created:</h3>";
    echo "<ul>";
    echo "<li><strong>Username:</strong> $username</li>";
    echo "<li><strong>Password:</strong> $password</li>";
    echo "<li><strong>Email:</strong> $email</li>";
    echo "<li><strong>Status:</strong> Active</li>";
    echo "</ul>";
    
    // Verify the file was created correctly
    if (file_exists($filename)) {
        $fileContent = file_get_contents($filename);
        $parsedUsers = json_decode($fileContent, true);
        
        if ($parsedUsers && count($parsedUsers) > 0) {
            echo "<p style='color: green;'>✅ File verification successful!</p>";
            echo "<p>Users in file: " . count($parsedUsers) . "</p>";
            
            // Test password verification
            if (password_verify($password, $parsedUsers[0]['password'])) {
                echo "<p style='color: green;'>✅ Password hash verification successful!</p>";
            } else {
                echo "<p style='color: red;'>❌ Password hash verification failed!</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ File verification failed - invalid JSON!</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ File was not created!</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ Failed to create users.json file!</p>";
    echo "<p>Check file permissions in the directory.</p>";
}

echo "<hr>";
echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li><a href='login.php'>Test Login</a></li>";
echo "<li>Delete this file (create_users_file.php) for security</li>";
echo "</ol>";

// Show the JSON content
echo "<h3>File Content:</h3>";
echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
echo htmlspecialchars($jsonData);
echo "</pre>";
?>

<style>
body { font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; }
h2, h3 { color: #333; }
p { line-height: 1.6; }
ul, ol { line-height: 1.8; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { overflow-x: auto; }
</style>

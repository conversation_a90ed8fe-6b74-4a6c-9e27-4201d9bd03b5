<?php
session_start();

// Check if user is logged in
$isLoggedIn = isset($_SESSION['user_logged_in']) && $_SESSION['user_logged_in'] === true;
$username = $isLoggedIn ? $_SESSION['username'] : '';
?>

<!DOCTYPE html>
<html>
<head>
    <title><?= isset($pageTitle) ? $pageTitle : 'Business Search Application' ?></title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-top {
            background: rgba(0,0,0,0.1);
            padding: 8px 0;
            font-size: 14px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .header-top-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .contact-info {
            display: flex;
            gap: 20px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .main-header {
            padding: 15px 0;
        }
        
        .main-header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            text-decoration: none;
            color: white;
        }
        
        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }
        
        .nav-menu a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background-color 0.3s;
            font-weight: 500;
        }
        
        .nav-menu a:hover,
        .nav-menu a.active {
            background-color: rgba(255,255,255,0.2);
        }
        
        .auth-buttons {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-block;
        }
        
        .btn-primary {
            background-color: #007cba;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #005a87;
        }
        
        .btn-secondary {
            background-color: transparent;
            color: white;
            border: 2px solid white;
        }
        
        .btn-secondary:hover {
            background-color: white;
            color: #667eea;
        }
        
        .btn-logout {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-logout:hover {
            background-color: #c82333;
        }
        
        .welcome-text {
            color: #f8f9fa;
        }
        
        .main-content {
            padding: 20px;
            min-height: calc(100vh - 140px);
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .header-top-content,
            .main-header-content {
                flex-direction: column;
                gap: 10px;
            }
            
            .nav-menu {
                flex-direction: column;
                gap: 10px;
                width: 100%;
                text-align: center;
            }
            
            .contact-info {
                flex-direction: column;
                gap: 5px;
                text-align: center;
            }
        }
    </style>
</head>
<body>

<header class="header">
    <div class="header-top">
        <div class="container">
            <div class="header-top-content">
                <div class="contact-info">
                    <span>📧 <EMAIL></span>
                    <span>📞 +91 98765 43210</span>
                </div>
                <div class="user-info">
                    <?php if ($isLoggedIn): ?>
                        <span class="welcome-text">Welcome, <?= htmlspecialchars($username) ?>!</span>
                        <a href="logout.php" class="btn btn-logout">Logout</a>
                    <?php else: ?>
                        <a href="login.php" class="btn btn-secondary">Login</a>
                        <a href="register.php" class="btn btn-primary">Register</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <div class="main-header">
        <div class="container">
            <div class="main-header-content">
                <a href="index.php" class="logo">🔍 Business Finder</a>
                
                <nav>
                    <ul class="nav-menu">
                        <li><a href="index.php" class="<?= basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : '' ?>">🏠 Home</a></li>
                        <?php if ($isLoggedIn): ?>
                            <li><a href="dashboard.php" class="<?= basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : '' ?>">📊 Dashboard</a></li>
                            <li><a href="search.php" class="<?= basename($_SERVER['PHP_SELF']) == 'search.php' ? 'active' : '' ?>">🔍 Search</a></li>
                            <li><a href="view-results.php" class="<?= basename($_SERVER['PHP_SELF']) == 'view-results.php' ? 'active' : '' ?>">📋 View Results</a></li>
                            <li><a href="admin.php" class="<?= basename($_SERVER['PHP_SELF']) == 'admin.php' ? 'active' : '' ?>">⚙️ Admin</a></li>
                        <?php else: ?>
                            <li><a href="about.php" class="<?= basename($_SERVER['PHP_SELF']) == 'about.php' ? 'active' : '' ?>">ℹ️ About</a></li>
                            <li><a href="contact.php" class="<?= basename($_SERVER['PHP_SELF']) == 'contact.php' ? 'active' : '' ?>">📞 Contact</a></li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</header>

<main class="main-content">
    <div class="container">

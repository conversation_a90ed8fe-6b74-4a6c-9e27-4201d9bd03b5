<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_logged_in']) || $_SESSION['user_logged_in'] !== true) {
    header("Location: login.php?redirect=admin.php");
    exit;
}

require 'db_connect.php';

// Get statistics from business_search_results table
$statsQuery = "SELECT
    COUNT(*) as total_results,
    COUNT(DISTINCT pincode) as unique_pincodes,
    COUNT(DISTINCT search_id) as total_searches,
    COUNT(DISTINCT DATE(search_timestamp)) as search_days
FROM business_search_results";
$statsResult = $conn->query($statsQuery);
$stats = $statsResult->fetch_assoc();

// Get recent searches (grouped by search_id)
$recentSearchesQuery = "SELECT search_id, pincode, keywords, COUNT(*) as result_count, MAX(search_timestamp) as latest_search
FROM business_search_results
GROUP BY search_id
ORDER BY latest_search DESC
LIMIT 10";
$recentSearches = $conn->query($recentSearchesQuery);

// Get top pincodes
$topPincodesQuery = "SELECT pincode, COUNT(DISTINCT search_id) as search_count, COUNT(*) as total_results
FROM business_search_results
GROUP BY pincode
ORDER BY search_count DESC
LIMIT 10";
$topPincodes = $conn->query($topPincodesQuery);

// Handle search results view
$searchResults = null;
$selectedSearchId = null;
if (isset($_GET['search_id'])) {
    $selectedSearchId = $_GET['search_id'];
    $resultsQuery = "SELECT * FROM business_search_results WHERE search_id = ? ORDER BY business_name";
    $stmt = $conn->prepare($resultsQuery);
    $stmt->bind_param("s", $selectedSearchId);
    $stmt->execute();
    $searchResults = $stmt->get_result();
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Business Search Admin Panel</title>
    <style>
        body { font-family: Arial; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
        .stat-item { text-align: center; padding: 15px; background: #007cba; color: white; border-radius: 5px; }
        .stat-number { font-size: 2em; font-weight: bold; }
        .stat-label { font-size: 0.9em; opacity: 0.9; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background-color: #f8f9fa; }
        .search-link { color: #007cba; text-decoration: none; }
        .search-link:hover { text-decoration: underline; }
        .back-link { display: inline-block; margin-bottom: 20px; padding: 8px 16px; background-color: #6c757d; color: white; text-decoration: none; border-radius: 4px; }
    </style>
</head>
<body>

<div class="container">
    <h1>Business Search Admin Panel</h1>
    
    <?php if ($selectedSearchId): ?>
        <a href="admin.php" class="back-link">← Back to Dashboard</a>
        
        <div class="card">
            <h2>Search Results for: <?= htmlspecialchars($selectedSearchId) ?></h2>
            
            <?php if ($searchResults && $searchResults->num_rows > 0): ?>
                <table>
                    <tr>
                        <th>Business Name</th>
                        <th>Address</th>
                        <th>Rating</th>
                        <th>Phone</th>
                        <th>Types</th>
                        <th>Place ID</th>
                    </tr>
                    <?php while ($row = $searchResults->fetch_assoc()): ?>
                        <tr>
                            <td><?= htmlspecialchars($row['business_name']) ?></td>
                            <td><?= htmlspecialchars($row['business_address']) ?></td>
                            <td><?= $row['business_rating'] ?? 'N/A' ?></td>
                            <td><?= htmlspecialchars($row['business_phone'] ?? 'N/A') ?></td>
                            <td><?= htmlspecialchars($row['business_types']) ?></td>
                            <td style="font-size: 12px;"><?= htmlspecialchars($row['place_id']) ?></td>
                        </tr>
                    <?php endwhile; ?>
                </table>
            <?php else: ?>
                <p>No results found for this search.</p>
            <?php endif; ?>
        </div>
        
    <?php else: ?>
        <!-- Dashboard View -->
        <div class="card">
            <h2>Statistics</h2>
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number"><?= $stats['total_searches'] ?></div>
                    <div class="stat-label">Total Searches</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= $stats['total_results'] ?></div>
                    <div class="stat-label">Total Results</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= $stats['unique_pincodes'] ?></div>
                    <div class="stat-label">Unique Pincodes</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= $stats['search_days'] ?></div>
                    <div class="stat-label">Active Days</div>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>Recent Searches</h2>
            <table>
                <tr>
                    <th>Search ID</th>
                    <th>Pincode</th>
                    <th>Keywords</th>
                    <th>Results</th>
                    <th>Latest Search</th>
                    <th>Action</th>
                </tr>
                <?php while ($row = $recentSearches->fetch_assoc()): ?>
                    <tr>
                        <td><?= htmlspecialchars($row['search_id']) ?></td>
                        <td><?= htmlspecialchars($row['pincode']) ?></td>
                        <td><?= htmlspecialchars($row['keywords'] ?: 'All types') ?></td>
                        <td><?= $row['result_count'] ?></td>
                        <td><?= $row['latest_search'] ?></td>
                        <td><a href="?search_id=<?= urlencode($row['search_id']) ?>" class="search-link">View Results</a></td>
                    </tr>
                <?php endwhile; ?>
            </table>
        </div>

        <div class="card">
            <h2>Top Pincodes</h2>
            <table>
                <tr>
                    <th>Pincode</th>
                    <th>Search Count</th>
                    <th>Total Results</th>
                </tr>
                <?php while ($row = $topPincodes->fetch_assoc()): ?>
                    <tr>
                        <td><?= htmlspecialchars($row['pincode']) ?></td>
                        <td><?= $row['search_count'] ?></td>
                        <td><?= $row['total_results'] ?></td>
                    </tr>
                <?php endwhile; ?>
            </table>
        </div>
    <?php endif; ?>
    
    <div class="card">
        <h3>👥 User Management</h3>
        <p><a href="register.php" class="search-link">Create New User Account</a></p>
        <p style="margin-top: 15px;"><a href="index.php" class="search-link">← Back to Home</a></p>
    </div>
</div>

</body>
</html>

<?php $conn->close(); ?>

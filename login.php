<?php
$pageTitle = "Login - Business Finder";

// Handle login form submission
$loginError = '';
$loginSuccess = '';

// Check for logout message
if (isset($_GET['message']) && $_GET['message'] === 'logged_out') {
    $loginSuccess = 'You have been successfully logged out.';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');
    
    if (empty($username) || empty($password)) {
        $loginError = 'Please enter both username and password.';
    } else {
        // Load users from JSON file
        $usersFile = 'users.json';
        $users = [];
        
        if (file_exists($usersFile)) {
            $usersData = file_get_contents($usersFile);
            $users = json_decode($usersData, true) ?? [];
        }
        
        // Check credentials
        $userFound = false;
        foreach ($users as $user) {
            if ($user['username'] === $username && password_verify($password, $user['password'])) {
                $userFound = true;
                
                // Start session and set login status
                session_start();
                $_SESSION['user_logged_in'] = true;
                $_SESSION['username'] = $user['username'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['login_time'] = time();
                
                // Update last login time
                foreach ($users as &$u) {
                    if ($u['username'] === $username) {
                        $u['last_login'] = date('Y-m-d H:i:s');
                        break;
                    }
                }
                file_put_contents($usersFile, json_encode($users, JSON_PRETTY_PRINT));
                
                // Redirect to dashboard or intended page
                $redirectTo = $_GET['redirect'] ?? 'dashboard.php';
                header("Location: $redirectTo");
                exit;
            }
        }
        
        if (!$userFound) {
            $loginError = 'Invalid username or password.';
        }
    }
}

include 'header.php';
?>

<style>
    .login-container {
        max-width: 400px;
        margin: 50px auto;
        background: white;
        padding: 40px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .login-header {
        text-align: center;
        margin-bottom: 30px;
    }
    
    .login-header h2 {
        color: #333;
        margin-bottom: 10px;
    }
    
    .login-header p {
        color: #666;
        font-size: 14px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        color: #333;
        font-weight: 500;
    }
    
    .form-group input {
        width: 100%;
        padding: 12px;
        border: 2px solid #ddd;
        border-radius: 5px;
        font-size: 16px;
        transition: border-color 0.3s;
    }
    
    .form-group input:focus {
        outline: none;
        border-color: #667eea;
    }
    
    .btn-login {
        width: 100%;
        padding: 12px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 5px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: transform 0.2s;
    }
    
    .btn-login:hover {
        transform: translateY(-2px);
    }
    
    .alert {
        padding: 12px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .alert-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    

    

</style>

<div class="login-container">
    <div class="login-header">
        <h2>🔐 Login</h2>
        <p>Access your Business Finder account</p>
    </div>
    
    <?php if ($loginError): ?>
        <div class="alert alert-error">
            <?= htmlspecialchars($loginError) ?>
        </div>
    <?php endif; ?>
    
    <?php if ($loginSuccess): ?>
        <div class="alert alert-success">
            <?= htmlspecialchars($loginSuccess) ?>
        </div>
    <?php endif; ?>
    

    
    <form method="POST" action="">
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" required 
                   value="<?= htmlspecialchars($_POST['username'] ?? '') ?>"
                   placeholder="Enter your username">
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required 
                   placeholder="Enter your password">
        </div>
        
        <button type="submit" class="btn-login">Login</button>
    </form>
    

</div>

<?php include 'footer.php'; ?>

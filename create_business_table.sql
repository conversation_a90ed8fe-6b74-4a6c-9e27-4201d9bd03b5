-- Create table for business search results
-- Run this in your live database: u111133901_gbm

CREATE TABLE IF NOT EXISTS business_search_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    search_id VARCHAR(50) NOT NULL,
    pincode VARCHAR(10) NOT NULL,
    keywords TEXT,
    business_name VARCHAR(255) NOT NULL,
    business_address TEXT,
    business_rating DECIMAL(2,1) DEFAULT NULL,
    business_phone VARCHAR(50),
    business_types TEXT,
    place_id VARCHAR(255) UNIQUE NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    search_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_pincode (pincode),
    INDEX idx_search_id (search_id),
    INDEX idx_place_id (place_id),
    INDEX idx_search_timestamp (search_timestamp)
);

<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_logged_in']) || $_SESSION['user_logged_in'] !== true) {
    header("Location: login.php?redirect=dashboard.php");
    exit;
}

$pageTitle = "Dashboard - Business Finder";
include 'header.php';
?>

<style>
    .dashboard-container {
        max-width: 1000px;
        margin: 0 auto;
    }
    
    .welcome-section {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        text-align: center;
    }
    
    .welcome-section h1 {
        color: #333;
        margin-bottom: 10px;
    }
    
    .welcome-section p {
        color: #666;
        font-size: 16px;
    }
    
    .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .dashboard-card {
        background: white;
        padding: 25px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        text-align: center;
        transition: transform 0.3s;
    }
    
    .dashboard-card:hover {
        transform: translateY(-5px);
    }
    
    .dashboard-card h3 {
        color: #333;
        margin-bottom: 15px;
        font-size: 20px;
    }
    
    .dashboard-card p {
        color: #666;
        margin-bottom: 20px;
        line-height: 1.6;
    }
    
    .dashboard-card .icon {
        font-size: 48px;
        margin-bottom: 15px;
        display: block;
    }
    
    .card-button {
        display: inline-block;
        padding: 12px 24px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 5px;
        font-weight: 500;
        transition: transform 0.2s;
    }
    
    .card-button:hover {
        transform: translateY(-2px);
    }
    
    .quick-stats {
        background: white;
        padding: 25px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .quick-stats h3 {
        color: #333;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
    }
    
    .stat-item {
        text-align: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
    }
    
    .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #667eea;
        display: block;
    }
    
    .stat-label {
        font-size: 14px;
        color: #666;
        margin-top: 5px;
    }
    
    .recent-activity {
        background: white;
        padding: 25px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .recent-activity h3 {
        color: #333;
        margin-bottom: 20px;
    }
    
    .activity-item {
        padding: 15px;
        border-left: 4px solid #667eea;
        background: #f8f9fa;
        margin-bottom: 10px;
        border-radius: 0 5px 5px 0;
    }
    
    .activity-time {
        font-size: 12px;
        color: #666;
    }
</style>

<div class="dashboard-container">
    <div class="welcome-section">
        <h1>Welcome back, <?= htmlspecialchars($_SESSION['username']) ?>! 👋</h1>
        <p>Ready to find businesses without websites? Use the tools below to get started.</p>
    </div>
    
    <div class="dashboard-grid">
        <div class="dashboard-card">
            <span class="icon">🔍</span>
            <h3>Search Businesses</h3>
            <p>Find businesses without websites using pincode and keyword combinations.</p>
            <a href="search.php" class="card-button">Start Searching</a>
        </div>
        
        <div class="dashboard-card">
            <span class="icon">📋</span>
            <h3>View Results</h3>
            <p>Browse and filter all your saved search results from the database.</p>
            <a href="view-results.php" class="card-button">View Results</a>
        </div>

        <div class="dashboard-card">
            <span class="icon">📊</span>
            <h3>Admin Panel</h3>
            <p>View analytics, statistics, and manage your search data.</p>
            <a href="admin.php" class="card-button">Admin Panel</a>
        </div>
        
        <div class="dashboard-card">
            <span class="icon">👥</span>
            <h3>User Management</h3>
            <p>Create new user accounts and manage system access for team members.</p>
            <a href="register.php" class="card-button">Create User</a>
        </div>
    </div>
    
    <div class="quick-stats">
        <h3>📈 Quick Stats</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-number">0</span>
                <div class="stat-label">Total Searches</div>
            </div>
            <div class="stat-item">
                <span class="stat-number">0</span>
                <div class="stat-label">Businesses Found</div>
            </div>
            <div class="stat-item">
                <span class="stat-number">0</span>
                <div class="stat-label">Saved Results</div>
            </div>
            <div class="stat-item">
                <span class="stat-number">0</span>
                <div class="stat-label">Unique Pincodes</div>
            </div>
        </div>
    </div>
    
    <div class="recent-activity">
        <h3>🕒 Recent Activity</h3>
        <div class="activity-item">
            <strong>Account Created</strong>
            <div class="activity-time">Welcome to Business Finder!</div>
        </div>
        <div style="text-align: center; color: #666; padding: 20px;">
            <p>No recent search activity. <a href="search.php" style="color: #667eea;">Start your first search</a></p>
        </div>
    </div>
</div>

<?php include 'footer.php'; ?>

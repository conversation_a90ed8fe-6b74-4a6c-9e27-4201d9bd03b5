<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_logged_in']) || $_SESSION['user_logged_in'] !== true) {
    header("Location: login.php?redirect=saved-searches.php");
    exit;
}

$pageTitle = "Saved Searches - Business Finder";
include 'header.php';
?>

<style>
    .saved-container {
        max-width: 1000px;
        margin: 0 auto;
    }
    
    .saved-header {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        text-align: center;
    }
    
    .saved-header h1 {
        color: #333;
        margin-bottom: 15px;
    }
    
    .saved-header p {
        color: #666;
        font-size: 16px;
    }
    
    .empty-state {
        background: white;
        padding: 60px 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        text-align: center;
    }
    
    .empty-state .icon {
        font-size: 64px;
        margin-bottom: 20px;
        opacity: 0.5;
    }
    
    .empty-state h3 {
        color: #333;
        margin-bottom: 15px;
    }
    
    .empty-state p {
        color: #666;
        margin-bottom: 30px;
        line-height: 1.6;
    }
    
    .btn {
        display: inline-block;
        padding: 12px 24px;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 500;
        transition: transform 0.2s;
        margin: 0 10px;
    }
    
    .btn:hover {
        transform: translateY(-2px);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
    }
    
    .feature-info {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 20px;
        margin-top: 30px;
    }
    
    .feature-info h4 {
        color: #856404;
        margin-bottom: 15px;
    }
    
    .feature-info ul {
        color: #856404;
        margin-left: 20px;
        line-height: 1.6;
    }
</style>

<div class="saved-container">
    <div class="saved-header">
        <h1>💾 Saved Searches</h1>
        <p>Manage and revisit your previous search results</p>
    </div>
    
    <div class="empty-state">
        <div class="icon">🔍</div>
        <h3>No Saved Searches Yet</h3>
        <p>You haven't saved any searches yet. Start by performing a search and saving the results you find valuable.</p>
        
        <a href="search.php" class="btn btn-primary">🚀 Start Searching</a>
        <a href="dashboard.php" class="btn btn-secondary">📊 Dashboard</a>
    </div>
    
    <div class="feature-info">
        <h4>🔮 Coming Soon: Enhanced Save Features</h4>
        <ul>
            <li><strong>Save Search Results:</strong> Bookmark interesting businesses for later follow-up</li>
            <li><strong>Search History:</strong> View and repeat your previous searches</li>
            <li><strong>Export Options:</strong> Download saved results in CSV or Excel format</li>
            <li><strong>Notes & Tags:</strong> Add personal notes and categorize saved businesses</li>
            <li><strong>Follow-up Tracking:</strong> Track your outreach efforts and responses</li>
            <li><strong>Team Sharing:</strong> Share saved searches with team members</li>
        </ul>
        
        <p style="margin-top: 15px; font-size: 14px;">
            <strong>Note:</strong> This feature is currently in development. For now, you can bookmark the search results pages or copy the information you need.
        </p>
    </div>
</div>

<?php include 'footer.php'; ?>

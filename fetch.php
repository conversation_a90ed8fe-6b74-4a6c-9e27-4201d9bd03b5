<?php
require 'config.php';

// Try to connect to database, but don't fail if it's not available
$dbConnected = false;
$conn = null;

try {
    require 'db_connect.php';
    $dbConnected = true;
} catch (Exception $e) {
    error_log("Database connection failed: " . $e->getMessage());
    $dbConnected = false;
}

if (!isset($_POST['pincode'])) {
    die("Pincode required");
}

$pincode = trim($_POST['pincode']);
$keywords = isset($_POST['keywords']) ? trim($_POST['keywords']) : '';
$apiKey = GOOGLE_API_KEY;

// Generate unique search ID
$searchId = 'search_' . date('Ymd_His') . '_' . substr(md5($pincode . $keywords . time()), 0, 8);

// Database functions
function saveSearchHistory($conn, $searchId, $pincode, $keywords, $totalResults) {
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

    $stmt = $conn->prepare("INSERT INTO search_history (search_id, pincode, keywords, total_results, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("sssiss", $searchId, $pincode, $keywords, $totalResults, $ipAddress, $userAgent);
    return $stmt->execute();
}

function saveBusinessResult($conn, $searchId, $pincode, $keywords, $businessData, $lat, $lng) {
    $stmt = $conn->prepare("INSERT INTO business_search_results (search_id, pincode, keywords, business_name, business_address, business_rating, business_phone, business_types, place_id, latitude, longitude) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE search_id = VALUES(search_id), updated_at = CURRENT_TIMESTAMP");

    $rating = ($businessData['rating'] !== 'N/A') ? $businessData['rating'] : null;
    $phone = ($businessData['phone'] !== 'N/A') ? $businessData['phone'] : null;

    $stmt->bind_param("sssssssssdd",
        $searchId,
        $pincode,
        $keywords,
        $businessData['name'],
        $businessData['address'],
        $rating,
        $phone,
        $businessData['types'],
        $businessData['place_id'],
        $lat,
        $lng
    );

    return $stmt->execute();
}

// Step 1: Geocode Pincode to Lat/Lng
$geoUrl = "https://maps.googleapis.com/maps/api/geocode/json?address=" . urlencode($pincode) . "&key=" . $apiKey;
$geoData = json_decode(file_get_contents($geoUrl), true);

if ($geoData['status'] !== 'OK') {
    die("Invalid pincode or API limit exceeded");
}

$location = $geoData['results'][0]['geometry']['location'];
$lat = $location['lat'];
$lng = $location['lng'];

// Step 2: Get Nearby Businesses with Pagination
$radius = 2000; // in meters
$type = "store"; // general business type

$results = [];
$pageCount = 0;
$nextPageToken = null;

// Function to search by keywords if provided
function searchByKeywords($lat, $lng, $radius, $keywords, $apiKey) {
    $keywordResults = [];
    $keywordArray = array_map('trim', explode(',', $keywords));

    foreach ($keywordArray as $keyword) {
        if (empty($keyword)) continue;

        $query = urlencode($keyword);
        $textSearchUrl = "https://maps.googleapis.com/maps/api/place/textsearch/json?query={$query}&location={$lat},{$lng}&radius={$radius}&key={$apiKey}";
        $textSearchData = json_decode(file_get_contents($textSearchUrl), true);

        if (isset($textSearchData['results'])) {
            $keywordResults = array_merge($keywordResults, $textSearchData['results']);
        }

        // Small delay to avoid hitting API limits
        usleep(100000); // 0.1 second delay
    }

    return $keywordResults;
}

// Combine results from both nearby search and keyword search
$allPlaces = [];

// If keywords are provided, search by keywords
if (!empty($keywords)) {
    $keywordPlaces = searchByKeywords($lat, $lng, $radius, $keywords, $apiKey);
    $allPlaces = array_merge($allPlaces, $keywordPlaces);
}

// Also do nearby search for general businesses
do {
    if ($nextPageToken) {
        sleep(4); // required delay for next_page_token
        $placesUrl = "https://maps.googleapis.com/maps/api/place/nearbysearch/json?pagetoken=$nextPageToken&key=$apiKey";
    } else {
        $placesUrl = "https://maps.googleapis.com/maps/api/place/nearbysearch/json?location={$lat},{$lng}&radius={$radius}&type={$type}&key=$apiKey";
    }

    $placesData = json_decode(file_get_contents($placesUrl), true);

    if (!isset($placesData['results'])) break;

    // Add nearby search results to all places
    $allPlaces = array_merge($allPlaces, $placesData['results']);

    $nextPageToken = $placesData['next_page_token'] ?? null;
    $pageCount++;

} while ($nextPageToken && $pageCount < 2); // Limit nearby search to 2 pages when combined with keyword search

// Remove duplicates based on place_id
$uniquePlaces = [];
$seenPlaceIds = [];

foreach ($allPlaces as $place) {
    $placeId = $place['place_id'];
    if (!in_array($placeId, $seenPlaceIds)) {
        $uniquePlaces[] = $place;
        $seenPlaceIds[] = $placeId;
    }
}

// Step 3: Process all unique places and filter those without websites
foreach ($uniquePlaces as $place) {
    $placeId = $place['place_id'];

    // Fetch details (for phone number, website)
    $detailsUrl = "https://maps.googleapis.com/maps/api/place/details/json?place_id={$placeId}&fields=name,formatted_address,rating,website,formatted_phone_number,types&key={$apiKey}";
    $detailsData = json_decode(file_get_contents($detailsUrl), true);

    if (isset($detailsData['result']) && !isset($detailsData['result']['website'])) {
        $info = $detailsData['result'];
        $businessTypes = isset($info['types']) ? implode(', ', array_slice($info['types'], 0, 3)) : 'N/A';

        $businessData = [
            'name' => $info['name'] ?? '',
            'address' => $info['formatted_address'] ?? '',
            'rating' => $info['rating'] ?? 'N/A',
            'phone' => $info['formatted_phone_number'] ?? 'N/A',
            'types' => $businessTypes,
            'place_id' => $placeId
        ];

        $results[] = $businessData;

        // Save to database if connected
        if ($dbConnected && $conn) {
            try {
                saveBusinessResult($conn, $searchId, $pincode, $keywords, $businessData, $lat, $lng);
            } catch (Exception $e) {
                // Log error but continue processing
                error_log("Database save error: " . $e->getMessage());
            }
        }
    }

    // Small delay to avoid hitting API limits
    usleep(50000); // 0.05 second delay
}

// Save search history if database is connected
if ($dbConnected && $conn) {
    try {
        saveSearchHistory($conn, $searchId, $pincode, $keywords, count($results));
    } catch (Exception $e) {
        error_log("Search history save error: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Results</title>
    <style>
        body { font-family: Arial; padding: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ccc; padding: 8px; text-align: left; }
        th { background-color: #f4f4f4; }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            padding: 8px 16px;
            background-color: #007cba;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .back-link:hover { background-color: #005a87; }
        .search-info { background-color: #f0f8ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
    </style>
</head>
<body>

<a href="index.php" class="back-link">← Back to Search</a>

<h2>Businesses Without Website</h2>
<div class="search-info">
    <p><strong>Search Parameters:</strong></p>
    <ul>
        <li><strong>Search ID:</strong> <?= htmlspecialchars($searchId) ?></li>
        <li><strong>Pincode:</strong> <?= htmlspecialchars($pincode) ?></li>
        <?php if (!empty($keywords)): ?>
            <li><strong>Keywords:</strong> <?= htmlspecialchars($keywords) ?></li>
        <?php else: ?>
            <li><strong>Keywords:</strong> All business types</li>
        <?php endif; ?>
        <li><strong>Results Found:</strong> <?= count($results) ?></li>
        <li><strong>Data Status:</strong>
            <?php if ($dbConnected): ?>
                <span style="color: green;">✓ Saved to Database</span>
            <?php else: ?>
                <span style="color: orange;">⚠ Database not connected (results not saved)</span>
            <?php endif; ?>
        </li>
    </ul>
</div>

<?php if (count($results) === 0): ?>
    <p>No businesses without websites found for the given search criteria.</p>
    <p><strong>Suggestions:</strong></p>
    <ul>
        <li>Try different keywords (e.g., restaurant, shop, clinic, salon)</li>
        <li>Check if the pincode is correct</li>
        <li>Try searching without keywords to see all business types</li>
    </ul>
<?php else: ?>
    <table>
        <tr>
            <th>Name</th>
            <th>Address</th>
            <th>Rating</th>
            <th>Phone</th>
            <th>Business Types</th>
            <th>Place ID</th>
        </tr>
        <?php foreach ($results as $row): ?>
            <tr>
                <td><?= htmlspecialchars($row['name']) ?></td>
                <td><?= htmlspecialchars($row['address']) ?></td>
                <td><?= htmlspecialchars($row['rating']) ?></td>
                <td><?= htmlspecialchars($row['phone']) ?></td>
                <td><?= htmlspecialchars($row['types']) ?></td>
                <td style="font-size: 12px;"><?= htmlspecialchars($row['place_id']) ?></td>
            </tr>
        <?php endforeach; ?>
    </table>

    <div style="margin-top: 20px; padding: 10px; background-color: #f9f9f9; border-left: 4px solid #4CAF50;">
        <h3>Search Tips:</h3>
        <ul>
            <li><strong>Keywords:</strong> Use specific terms like "restaurant", "medical", "beauty salon", "grocery store"</li>
            <li><strong>Multiple Keywords:</strong> Separate with commas: "restaurant, cafe, food"</li>
            <li><strong>Broader Search:</strong> Leave keywords empty to search all business types</li>
        </ul>

        <?php if ($dbConnected): ?>
            <p style="margin-top: 15px;">
                <a href="admin.php" style="color: #007cba; text-decoration: none;">📊 View Admin Dashboard</a> |
                <a href="admin.php?search_id=<?= urlencode($searchId) ?>" style="color: #007cba; text-decoration: none;">🔍 View This Search in Database</a>
            </p>
        <?php endif; ?>
    </div>
<?php endif; ?>

</body>
</html>

<?php
require 'config.php';

// Try to connect to database
$dbConnected = false;
$conn = null;

try {
    require 'db_connect.php';
    $dbConnected = true;
} catch (Exception $e) {
    error_log("Database connection failed: " . $e->getMessage());
    $dbConnected = false;
}

if (!isset($_POST['pincode'])) {
    die("Pincode required");
}

$pincode = trim($_POST['pincode']);
$keywords = isset($_POST['keywords']) ? trim($_POST['keywords']) : '';
$apiKey = GOOGLE_API_KEY;

// Generate unique search ID for this session
$searchId = 'search_' . date('Ymd_His') . '_' . substr(md5($pincode . $keywords . time()), 0, 8);

// Database functions
function saveBusinessResult($conn, $searchId, $pincode, $keywords, $businessData, $lat, $lng) {
    $stmt = $conn->prepare("INSERT INTO business_search_results (search_id, pincode, keywords, business_name, business_address, business_rating, business_phone, business_types, place_id, latitude, longitude) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE search_id = VALUES(search_id), updated_at = CURRENT_TIMESTAMP");

    $rating = ($businessData['rating'] !== 'N/A') ? $businessData['rating'] : null;
    $phone = ($businessData['phone'] !== 'N/A') ? $businessData['phone'] : null;

    $stmt->bind_param("sssssssssdd",
        $searchId,
        $pincode,
        $keywords,
        $businessData['name'],
        $businessData['address'],
        $rating,
        $phone,
        $businessData['types'],
        $businessData['place_id'],
        $lat,
        $lng
    );

    return $stmt->execute();
}

// Step 1: Geocode Pincode to Lat/Lng
$geoUrl = "https://maps.googleapis.com/maps/api/geocode/json?address=" . urlencode($pincode) . "&key=" . $apiKey;
$geoData = json_decode(file_get_contents($geoUrl), true);

if ($geoData['status'] !== 'OK') {
    die("Invalid pincode or API limit exceeded");
}

$location = $geoData['results'][0]['geometry']['location'];
$lat = $location['lat'];
$lng = $location['lng'];

// Step 2: Get Nearby Businesses with Pagination
$radius = 2000; // in meters
$type = "store"; // general business type

$results = [];
$pageCount = 0;
$nextPageToken = null;
$savedCount = 0; // Track how many records were saved to database

// Function to search by keywords if provided
function searchByKeywords($lat, $lng, $radius, $keywords, $apiKey) {
    $keywordResults = [];
    $keywordArray = array_map('trim', explode(',', $keywords));

    foreach ($keywordArray as $keyword) {
        if (empty($keyword)) continue;

        $query = urlencode($keyword);
        $textSearchUrl = "https://maps.googleapis.com/maps/api/place/textsearch/json?query={$query}&location={$lat},{$lng}&radius={$radius}&key={$apiKey}";
        $textSearchData = json_decode(file_get_contents($textSearchUrl), true);

        if (isset($textSearchData['results'])) {
            $keywordResults = array_merge($keywordResults, $textSearchData['results']);
        }

        // Small delay to avoid hitting API limits
        usleep(100000); // 0.1 second delay
    }

    return $keywordResults;
}

// Combine results from both nearby search and keyword search
$allPlaces = [];

// If keywords are provided, search by keywords
if (!empty($keywords)) {
    $keywordPlaces = searchByKeywords($lat, $lng, $radius, $keywords, $apiKey);
    $allPlaces = array_merge($allPlaces, $keywordPlaces);
}

// Also do nearby search for general businesses
do {
    if ($nextPageToken) {
        sleep(4); // required delay for next_page_token
        $placesUrl = "https://maps.googleapis.com/maps/api/place/nearbysearch/json?pagetoken=$nextPageToken&key=$apiKey";
    } else {
        $placesUrl = "https://maps.googleapis.com/maps/api/place/nearbysearch/json?location={$lat},{$lng}&radius={$radius}&type={$type}&key=$apiKey";
    }

    $placesData = json_decode(file_get_contents($placesUrl), true);

    if (!isset($placesData['results'])) break;

    // Add nearby search results to all places
    $allPlaces = array_merge($allPlaces, $placesData['results']);

    $nextPageToken = $placesData['next_page_token'] ?? null;
    $pageCount++;

} while ($nextPageToken && $pageCount < 2); // Limit nearby search to 2 pages when combined with keyword search

// Remove duplicates based on place_id
$uniquePlaces = [];
$seenPlaceIds = [];

foreach ($allPlaces as $place) {
    $placeId = $place['place_id'];
    if (!in_array($placeId, $seenPlaceIds)) {
        $uniquePlaces[] = $place;
        $seenPlaceIds[] = $placeId;
    }
}

// Step 3: Process all unique places and filter those without websites
foreach ($uniquePlaces as $place) {
    $placeId = $place['place_id'];

    // Fetch details (for phone number, website)
    $detailsUrl = "https://maps.googleapis.com/maps/api/place/details/json?place_id={$placeId}&fields=name,formatted_address,rating,website,formatted_phone_number,types&key={$apiKey}";
    $detailsData = json_decode(file_get_contents($detailsUrl), true);

    if (isset($detailsData['result']) && !isset($detailsData['result']['website'])) {
        $info = $detailsData['result'];
        $businessTypes = isset($info['types']) ? implode(', ', array_slice($info['types'], 0, 3)) : 'N/A';

        $businessData = [
            'name' => $info['name'] ?? '',
            'address' => $info['formatted_address'] ?? '',
            'rating' => $info['rating'] ?? 'N/A',
            'phone' => $info['formatted_phone_number'] ?? 'N/A',
            'types' => $businessTypes,
            'place_id' => $placeId
        ];

        $results[] = $businessData;

        // Save to database if connected
        if ($dbConnected && $conn) {
            try {
                $saveResult = saveBusinessResult($conn, $searchId, $pincode, $keywords, $businessData, $lat, $lng);
                if ($saveResult) {
                    $savedCount++;
                } else {
                    error_log("Database save failed for business: " . $businessData['name']);
                }
            } catch (Exception $e) {
                // Log error but continue processing
                error_log("Database save error: " . $e->getMessage());
            }
        }


    }

    // Small delay to avoid hitting API limits
    usleep(50000); // 0.05 second delay
}


$pageTitle = "Search Results - Business Finder";
include 'header.php';
?>

<style>
    .results-container {
        max-width: 1200px;
        margin: 0 auto;
    }

    .search-info {
        background: white;
        padding: 25px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .search-info h2 {
        color: #333;
        margin-bottom: 20px;
    }

    .search-params {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 15px;
    }

    .param-item {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #667eea;
    }

    .param-label {
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
    }

    .param-value {
        color: #666;
    }

    .results-table-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .results-table {
        width: 100%;
        border-collapse: collapse;
    }

    .results-table th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px;
        text-align: left;
        font-weight: 500;
    }

    .results-table td {
        padding: 15px;
        border-bottom: 1px solid #eee;
        vertical-align: top;
    }

    .results-table tr:hover {
        background-color: #f8f9fa;
    }

    .no-results {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }

    .no-results h3 {
        color: #333;
        margin-bottom: 15px;
    }

    .suggestions {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 20px;
        margin-top: 20px;
    }

    .suggestions h4 {
        color: #856404;
        margin-bottom: 15px;
    }

    .suggestions ul {
        color: #856404;
        margin-left: 20px;
    }

    .search-tips {
        background: white;
        padding: 25px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-top: 30px;
        border-left: 4px solid #28a745;
    }

    .search-tips h3 {
        color: #333;
        margin-bottom: 15px;
    }

    .search-tips ul {
        color: #666;
        line-height: 1.8;
    }

    .action-buttons {
        text-align: center;
        margin-top: 30px;
    }

    .btn {
        display: inline-block;
        padding: 12px 24px;
        margin: 0 10px;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 500;
        transition: transform 0.2s;
    }

    .btn:hover {
        transform: translateY(-2px);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }
</style>

<div class="results-container">

    <div class="search-info">
        <h2>🔍 Search Results</h2>
        <div class="search-params">
            <div class="param-item">
                <div class="param-label">📍 Pincode</div>
                <div class="param-value"><?= htmlspecialchars($pincode) ?></div>
            </div>
            <div class="param-item">
                <div class="param-label">🏷️ Keywords</div>
                <div class="param-value">
                    <?= !empty($keywords) ? htmlspecialchars($keywords) : 'All business types' ?>
                </div>
            </div>
            <div class="param-item">
                <div class="param-label">📊 Results Found</div>
                <div class="param-value"><?= count($results) ?> businesses</div>
            </div>
            <div class="param-item">
                <div class="param-label">🆔 Search ID</div>
                <div class="param-value"><?= htmlspecialchars($searchId) ?></div>
            </div>
            <div class="param-item">
                <div class="param-label">💾 Database Status</div>
                <div class="param-value">
                    <?php if ($dbConnected): ?>
                        <span style="color: green;">✓ Connected - Saved <?= $savedCount ?>/<?= count($results) ?> records</span>
                    <?php else: ?>
                        <span style="color: orange;">⚠ Not Connected</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <?php if (count($results) === 0): ?>
        <div class="no-results">
            <h3>🔍 No Results Found</h3>
            <p>No businesses without websites found for the given search criteria.</p>

            <div class="suggestions">
                <h4>💡 Suggestions:</h4>
                <ul>
                    <li>Try different keywords (e.g., restaurant, shop, clinic, salon)</li>
                    <li>Check if the pincode is correct</li>
                    <li>Try searching without keywords to see all business types</li>
                    <li>Use broader terms like "store", "service", "food"</li>
                </ul>
            </div>
        </div>
    <?php else: ?>
        <div class="results-table-container">
            <table class="results-table">
                <thead>
                    <tr>
                        <th>🏢 Business Name</th>
                        <th>📍 Address</th>
                        <th>⭐ Rating</th>
                        <th>📞 Phone</th>
                        <th>🏷️ Business Types</th>
                        <th>🆔 Place ID</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($results as $row): ?>
                        <tr>
                            <td><strong><?= htmlspecialchars($row['name']) ?></strong></td>
                            <td><?= htmlspecialchars($row['address']) ?></td>
                            <td><?= $row['rating'] !== 'N/A' ? '⭐ ' . htmlspecialchars($row['rating']) : 'No rating' ?></td>
                            <td><?= $row['phone'] !== 'N/A' ? htmlspecialchars($row['phone']) : 'Not available' ?></td>
                            <td><?= htmlspecialchars($row['types']) ?></td>
                            <td style="font-size: 12px; font-family: monospace;"><?= htmlspecialchars($row['place_id']) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>

    <div class="search-tips">
        <h3>🎯 Search Tips for Better Results</h3>
        <ul>
            <li><strong>Specific Keywords:</strong> Use precise terms like "restaurant", "medical clinic", "beauty salon"</li>
            <li><strong>Multiple Keywords:</strong> Combine related terms: "restaurant, cafe, food, dining"</li>
            <li><strong>Broader Search:</strong> Leave keywords empty to discover all business types</li>
            <li><strong>Location Accuracy:</strong> Ensure the pincode is correct for your target area</li>
            <li><strong>Business Categories:</strong> Try terms like "retail", "healthcare", "automotive", "services"</li>
        </ul>
    </div>

    <div class="action-buttons">
        <a href="search.php" class="btn btn-primary">🔍 New Search</a>
        <a href="index.php" class="btn btn-secondary">🏠 Home</a>
    </div>
</div>

<?php include 'footer.php'; ?>

<?php
require 'config.php';

if (!isset($_POST['pincode'])) {
    die("Pincode required");
}

$pincode = trim($_POST['pincode']);
$apiKey = GOOGLE_API_KEY;

// Step 1: Geocode Pincode to Lat/Lng
$geoUrl = "https://maps.googleapis.com/maps/api/geocode/json?address=" . urlencode($pincode) . "&key=" . $apiKey;
$geoData = json_decode(file_get_contents($geoUrl), true);

if ($geoData['status'] !== 'OK') {
    die("Invalid pincode or API limit exceeded");
}

$location = $geoData['results'][0]['geometry']['location'];
$lat = $location['lat'];
$lng = $location['lng'];

// Step 2: Get Nearby Businesses with Pagination
$radius = 2000; // in meters
$type = "store"; // general business type

$results = [];
$pageCount = 0;
$nextPageToken = null;

do {
    if ($nextPageToken) {
        sleep(4); // required delay for next_page_token
        $placesUrl = "https://maps.googleapis.com/maps/api/place/nearbysearch/json?pagetoken=$nextPageToken&key=$apiKey";
    } else {
        $placesUrl = "https://maps.googleapis.com/maps/api/place/nearbysearch/json?location={$lat},{$lng}&radius={$radius}&type={$type}&key=$apiKey";
    }

    $placesData = json_decode(file_get_contents($placesUrl), true);

    if (!isset($placesData['results'])) break;

    foreach ($placesData['results'] as $place) {
        $placeId = $place['place_id'];

        // Step 3: Fetch details (for phone number, website)
        $detailsUrl = "https://maps.googleapis.com/maps/api/place/details/json?place_id={$placeId}&fields=name,formatted_address,rating,website,formatted_phone_number&key={$apiKey}";
        $detailsData = json_decode(file_get_contents($detailsUrl), true);

        if (isset($detailsData['result']) && !isset($detailsData['result']['website'])) {
            $info = $detailsData['result'];
            $results[] = [
                'name' => $info['name'] ?? '',
                'address' => $info['formatted_address'] ?? '',
                'rating' => $info['rating'] ?? 'N/A',
                'phone' => $info['formatted_phone_number'] ?? 'N/A',
                'place_id' => $placeId
            ];
        }
    }

    $nextPageToken = $placesData['next_page_token'] ?? null;
    $pageCount++;

} while ($nextPageToken && $pageCount < 3); // Max 3 pages (approx. 60 results)
?>

<!DOCTYPE html>
<html>
<head>
    <title>Results</title>
    <style>
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ccc; padding: 8px; text-align: left; }
        th { background-color: #f4f4f4; }
    </style>
</head>
<body>

<h2>Businesses Without Website Near Pincode: <?= htmlspecialchars($pincode) ?></h2>

<?php if (count($results) === 0): ?>
    <p>No businesses without websites found for this pincode.</p>
<?php else: ?>
    <table>
        <tr>
            <th>Name</th>
            <th>Address</th>
            <th>Rating</th>
            <th>Phone</th>
            <th>Place ID</th>
        </tr>
        <?php foreach ($results as $row): ?>
            <tr>
                <td><?= htmlspecialchars($row['name']) ?></td>
                <td><?= htmlspecialchars($row['address']) ?></td>
                <td><?= htmlspecialchars($row['rating']) ?></td>
                <td><?= htmlspecialchars($row['phone']) ?></td>
                <td><?= htmlspecialchars($row['place_id']) ?></td>
            </tr>
        <?php endforeach; ?>
    </table>
<?php endif; ?>

</body>
</html>

<?php
require 'config.php';

if (!isset($_POST['pincode'])) {
    die("Pincode required");
}

$pincode = trim($_POST['pincode']);
$keywords = isset($_POST['keywords']) ? trim($_POST['keywords']) : '';
$apiKey = GOOGLE_API_KEY;

// Step 1: Geocode Pincode to Lat/Lng
$geoUrl = "https://maps.googleapis.com/maps/api/geocode/json?address=" . urlencode($pincode) . "&key=" . $apiKey;
$geoData = json_decode(file_get_contents($geoUrl), true);

if ($geoData['status'] !== 'OK') {
    die("Invalid pincode or API limit exceeded");
}

$location = $geoData['results'][0]['geometry']['location'];
$lat = $location['lat'];
$lng = $location['lng'];

// Step 2: Get Nearby Businesses with Pagination
$radius = 2000; // in meters
$type = "store"; // general business type

$results = [];
$pageCount = 0;
$nextPageToken = null;

// Function to search by keywords if provided
function searchByKeywords($lat, $lng, $radius, $keywords, $apiKey) {
    $keywordResults = [];
    $keywordArray = array_map('trim', explode(',', $keywords));

    foreach ($keywordArray as $keyword) {
        if (empty($keyword)) continue;

        $query = urlencode($keyword);
        $textSearchUrl = "https://maps.googleapis.com/maps/api/place/textsearch/json?query={$query}&location={$lat},{$lng}&radius={$radius}&key={$apiKey}";
        $textSearchData = json_decode(file_get_contents($textSearchUrl), true);

        if (isset($textSearchData['results'])) {
            $keywordResults = array_merge($keywordResults, $textSearchData['results']);
        }

        // Small delay to avoid hitting API limits
        usleep(100000); // 0.1 second delay
    }

    return $keywordResults;
}

// Combine results from both nearby search and keyword search
$allPlaces = [];

// If keywords are provided, search by keywords
if (!empty($keywords)) {
    $keywordPlaces = searchByKeywords($lat, $lng, $radius, $keywords, $apiKey);
    $allPlaces = array_merge($allPlaces, $keywordPlaces);
}

// Also do nearby search for general businesses
do {
    if ($nextPageToken) {
        sleep(4); // required delay for next_page_token
        $placesUrl = "https://maps.googleapis.com/maps/api/place/nearbysearch/json?pagetoken=$nextPageToken&key=$apiKey";
    } else {
        $placesUrl = "https://maps.googleapis.com/maps/api/place/nearbysearch/json?location={$lat},{$lng}&radius={$radius}&type={$type}&key=$apiKey";
    }

    $placesData = json_decode(file_get_contents($placesUrl), true);

    if (!isset($placesData['results'])) break;

    // Add nearby search results to all places
    $allPlaces = array_merge($allPlaces, $placesData['results']);

    $nextPageToken = $placesData['next_page_token'] ?? null;
    $pageCount++;

} while ($nextPageToken && $pageCount < 2); // Limit nearby search to 2 pages when combined with keyword search

// Remove duplicates based on place_id
$uniquePlaces = [];
$seenPlaceIds = [];

foreach ($allPlaces as $place) {
    $placeId = $place['place_id'];
    if (!in_array($placeId, $seenPlaceIds)) {
        $uniquePlaces[] = $place;
        $seenPlaceIds[] = $placeId;
    }
}

// Step 3: Process all unique places and filter those without websites
foreach ($uniquePlaces as $place) {
    $placeId = $place['place_id'];

    // Fetch details (for phone number, website)
    $detailsUrl = "https://maps.googleapis.com/maps/api/place/details/json?place_id={$placeId}&fields=name,formatted_address,rating,website,formatted_phone_number,types&key={$apiKey}";
    $detailsData = json_decode(file_get_contents($detailsUrl), true);

    if (isset($detailsData['result']) && !isset($detailsData['result']['website'])) {
        $info = $detailsData['result'];
        $businessTypes = isset($info['types']) ? implode(', ', array_slice($info['types'], 0, 3)) : 'N/A';

        $results[] = [
            'name' => $info['name'] ?? '',
            'address' => $info['formatted_address'] ?? '',
            'rating' => $info['rating'] ?? 'N/A',
            'phone' => $info['formatted_phone_number'] ?? 'N/A',
            'types' => $businessTypes,
            'place_id' => $placeId
        ];
    }

    // Small delay to avoid hitting API limits
    usleep(50000); // 0.05 second delay
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Results</title>
    <style>
        body { font-family: Arial; padding: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ccc; padding: 8px; text-align: left; }
        th { background-color: #f4f4f4; }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            padding: 8px 16px;
            background-color: #007cba;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .back-link:hover { background-color: #005a87; }
        .search-info { background-color: #f0f8ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
    </style>
</head>
<body>

<a href="index.php" class="back-link">← Back to Search</a>

<h2>Businesses Without Website</h2>
<div class="search-info">
    <p><strong>Search Parameters:</strong></p>
    <ul>
        <li><strong>Pincode:</strong> <?= htmlspecialchars($pincode) ?></li>
        <?php if (!empty($keywords)): ?>
            <li><strong>Keywords:</strong> <?= htmlspecialchars($keywords) ?></li>
        <?php else: ?>
            <li><strong>Keywords:</strong> All business types</li>
        <?php endif; ?>
        <li><strong>Results Found:</strong> <?= count($results) ?></li>
    </ul>
</div>

<?php if (count($results) === 0): ?>
    <p>No businesses without websites found for the given search criteria.</p>
    <p><strong>Suggestions:</strong></p>
    <ul>
        <li>Try different keywords (e.g., restaurant, shop, clinic, salon)</li>
        <li>Check if the pincode is correct</li>
        <li>Try searching without keywords to see all business types</li>
    </ul>
<?php else: ?>
    <table>
        <tr>
            <th>Name</th>
            <th>Address</th>
            <th>Rating</th>
            <th>Phone</th>
            <th>Business Types</th>
            <th>Place ID</th>
        </tr>
        <?php foreach ($results as $row): ?>
            <tr>
                <td><?= htmlspecialchars($row['name']) ?></td>
                <td><?= htmlspecialchars($row['address']) ?></td>
                <td><?= htmlspecialchars($row['rating']) ?></td>
                <td><?= htmlspecialchars($row['phone']) ?></td>
                <td><?= htmlspecialchars($row['types']) ?></td>
                <td style="font-size: 12px;"><?= htmlspecialchars($row['place_id']) ?></td>
            </tr>
        <?php endforeach; ?>
    </table>

    <div style="margin-top: 20px; padding: 10px; background-color: #f9f9f9; border-left: 4px solid #4CAF50;">
        <h3>Search Tips:</h3>
        <ul>
            <li><strong>Keywords:</strong> Use specific terms like "restaurant", "medical", "beauty salon", "grocery store"</li>
            <li><strong>Multiple Keywords:</strong> Separate with commas: "restaurant, cafe, food"</li>
            <li><strong>Broader Search:</strong> Leave keywords empty to search all business types</li>
        </ul>
    </div>
<?php endif; ?>

</body>
</html>

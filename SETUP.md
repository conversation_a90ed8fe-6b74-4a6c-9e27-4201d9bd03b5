# Business Finder - Production Setup Guide

## 🚀 Quick Setup

### 1. Database Setup
Run this SQL in your MySQL database (`u111133901_gbm`):

```sql
CREATE TABLE IF NOT EXISTS business_search_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    search_id VARCHAR(50) NOT NULL,
    pincode VARCHAR(10) NOT NULL,
    keywords TEXT,
    business_name VARCHAR(255) NOT NULL,
    business_address TEXT,
    business_rating DECIMAL(2,1) DEFAULT NULL,
    business_phone VARCHAR(50),
    business_types TEXT,
    place_id VARCHAR(255) UNIQUE NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    search_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_pincode (pincode),
    INDEX idx_search_id (search_id),
    INDEX idx_place_id (place_id),
    INDEX idx_search_timestamp (search_timestamp)
);
```

### 2. Configuration
Update `config.php` with your Google Places API key:
```php
define("GOOGLE_API_KEY", "your_actual_api_key_here");
```

Update `db_connect.php` with your database credentials (already configured for your server).

### 3. File Permissions
Ensure `users.json` is writable by the web server:
```bash
chmod 666 users.json
```

### 4. User Access
- User account has been pre-created
- Login with provided credentials
- No registration functionality available
- Single-user system for security

## 🔧 Features

- **Login Protected**: All search functionality requires authentication
- **Database Storage**: All search results automatically saved
- **Duplicate Prevention**: Same businesses won't be saved twice
- **Professional UI**: Clean, responsive design
- **Data Management**: View, filter, and manage all saved results

## 📁 Core Files

- `index.php` - Landing page
- `login.php` / `register.php` - Authentication
- `search.php` - Search interface (protected)
- `fetch.php` - Search processor (protected)
- `view-results.php` - Database viewer (protected)
- `admin.php` - Analytics dashboard (protected)
- `config.php` - API configuration
- `db_connect.php` - Database connection
- `users.json` - User storage

## 🔒 Security

- Password hashing with PHP's `password_hash()`
- Session-based authentication
- Input validation and sanitization
- Protected search functionality
- Secure user management

## 📞 Support

For issues or questions, check the contact page or refer to the main README.md file.

# Business Finder - Enhanced Version

A modern web application to find businesses without websites using pincode and keyword combinations.

## 🚀 New Features

### ✅ **Login-Protected Search System**
- Search functionality requires user authentication
- All search results saved to database
- User-specific access control
- Professional user management

### ✅ **Professional Header & Navigation**
- Modern responsive header with gradient design
- User authentication status display
- Clean navigation menu with icons
- Mobile-friendly responsive design

### ✅ **JSON-Based Authentication System**
- User registration and login
- Password hashing for security
- Session management
- User data stored in `users.json`

### ✅ **Complete Page Structure**
- **Home Page** (`index.php`) - Landing page (different content for logged-in users)
- **Search Page** (`search.php`) - Protected search interface (login required)
- **Results Page** (`fetch.php`) - Enhanced results display (login required)
- **View Results** (`view-results.php`) - Browse saved database results (login required)
- **Dashboard** (`dashboard.php`) - User dashboard (login required)
- **Admin Panel** (`admin.php`) - Analytics and statistics (login required)
- **About Page** (`about.php`) - Information about the platform (public)
- **Contact Page** (`contact.php`) - Contact information and FAQ (public)
- **Login/Register** - User authentication pages (public)

## 🔧 Installation & Setup

### Prerequisites
- PHP 7.4 or higher
- Web server (Apache/Nginx) or PHP built-in server
- Google Places API key

### Quick Start
1. **Clone/Download** the project files
2. **Configure API Key** in `config.php`:
   ```php
   define("GOOGLE_API_KEY", "your_google_places_api_key_here");
   ```
3. **Start the server**:
   ```bash
   php -S localhost:8000
   ```
4. **Open browser** and visit `http://localhost:8000`

### Default Login Credentials
- **Username:** `admin`
- **Password:** `admin123`

## 📁 File Structure

```
├── index.php              # Home page
├── search.php             # Search interface
├── fetch.php              # Search results
├── login.php              # User login
├── register.php           # User registration
├── logout.php             # Logout handler
├── dashboard.php          # User dashboard
├── about.php              # About page
├── contact.php            # Contact page
├── saved-searches.php     # Saved searches (placeholder)
├── header.php             # Common header
├── footer.php             # Common footer
├── config.php             # API configuration
├── users.json             # User data storage
└── README.md              # This file
```

## 🎯 Key Features

### **Enhanced Search**
- **Location-based:** Search by Indian pincode
- **Keyword filtering:** Find specific business types
- **Combined search:** Pincode + keywords for targeted results
- **Real-time data:** Google Places API integration

### **User Management**
- **Registration:** Create new accounts
- **Login/Logout:** Secure authentication
- **Dashboard:** Personalized user interface
- **Session management:** Secure user sessions

### **Modern UI/UX**
- **Responsive design:** Works on all devices
- **Professional styling:** Modern gradient themes
- **Intuitive navigation:** Easy-to-use interface
- **Loading states:** User feedback during searches

### **Business Intelligence**
- **No-website filter:** Focus on businesses without websites
- **Contact information:** Phone numbers and addresses
- **Business categories:** Industry classification
- **Rating information:** Google ratings when available

## 🔍 How to Use

### **For Visitors (No Login Required)**
1. Visit the home page
2. Enter a pincode (e.g., 110001)
3. Optionally add keywords (e.g., "restaurant, cafe")
4. Click "Search Businesses"
5. View results with business details

### **For Registered Users**
1. Register an account or login
2. Access the dashboard for personalized features
3. Use the search functionality
4. Save searches for future reference (coming soon)

### **Search Examples**
- **Restaurants in Delhi:** Pincode: `110001`, Keywords: `restaurant, cafe, food`
- **Medical clinics in Mumbai:** Pincode: `400001`, Keywords: `clinic, doctor, medical`
- **All businesses in Bangalore:** Pincode: `560001`, Keywords: (leave empty)

## 🛠️ Technical Details

### **Authentication System**
- **Storage:** JSON file (`users.json`)
- **Security:** Password hashing with PHP's `password_hash()`
- **Sessions:** PHP session management
- **Validation:** Input validation and sanitization

### **API Integration**
- **Google Places API:** Business data source
- **Geocoding:** Pincode to coordinates conversion
- **Rate limiting:** Respects API limits
- **Error handling:** Graceful failure management

### **Performance**
- **No database:** Faster startup and deployment
- **Efficient caching:** Minimal API calls
- **Responsive design:** Fast loading on all devices

## 🔒 Security Features

- **Password hashing:** Secure password storage
- **Session management:** Secure user sessions
- **Input validation:** XSS and injection protection
- **CSRF protection:** Form security (recommended to add)

## 🚀 Future Enhancements

- **Save search results:** Bookmark businesses
- **Export functionality:** CSV/Excel downloads
- **Advanced filtering:** More search options
- **Team collaboration:** Share searches
- **API access:** Developer API
- **Analytics dashboard:** Usage statistics

## 📞 Support

For questions or issues:
- **Email:** <EMAIL>
- **Phone:** +91 98765 43210
- **Documentation:** Check the About and Contact pages

## 📄 License

This project is for educational and commercial use. Please respect Google Places API terms of service.

---

**Ready to find businesses without websites?** Start searching now! 🔍

<?php
require 'db_connect.php';

echo "Setting up database tables...\n";

// Read and execute SQL file
$sql = file_get_contents('create_table.sql');

// Split SQL into individual statements
$statements = array_filter(array_map('trim', explode(';', $sql)));

$success = true;

foreach ($statements as $statement) {
    if (!empty($statement)) {
        echo "Executing: " . substr($statement, 0, 50) . "...\n";
        
        if ($conn->query($statement) === TRUE) {
            echo "✓ Success\n";
        } else {
            echo "✗ Error: " . $conn->error . "\n";
            $success = false;
        }
    }
}

if ($success) {
    echo "\n✓ Database setup completed successfully!\n";
    echo "Tables created:\n";
    echo "- business_search_results\n";
    echo "- search_history\n";
} else {
    echo "\n✗ Database setup failed. Please check the errors above.\n";
}

$conn->close();
?>

<?php
// Setup database table for business search results
// Run this file once on your live server to create the table

require 'db_connect.php';

echo "<h2>Setting up Business Search Results Table</h2>";

// SQL to create the table
$sql = "CREATE TABLE IF NOT EXISTS business_search_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    search_id VARCHAR(50) NOT NULL,
    pincode VA<PERSON>HAR(10) NOT NULL,
    keywords TEXT,
    business_name VARCHAR(255) NOT NULL,
    business_address TEXT,
    business_rating DECIMAL(2,1) DEFAULT NULL,
    business_phone VARCHAR(50),
    business_types TEXT,
    place_id VARCHAR(255) UNIQUE NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    search_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_pincode (pincode),
    INDEX idx_search_id (search_id),
    INDEX idx_place_id (place_id),
    INDEX idx_search_timestamp (search_timestamp)
)";

if ($conn->query($sql) === TRUE) {
    echo "<p style='color: green;'>✅ Table 'business_search_results' created successfully!</p>";
    
    // Check if table exists and show structure
    $result = $conn->query("DESCRIBE business_search_results");
    if ($result) {
        echo "<h3>Table Structure:</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Database table is ready</li>";
    echo "<li>🔍 Go to your search page and perform a search</li>";
    echo "<li>💾 Results will now be automatically saved to the database</li>";
    echo "<li>🗑️ You can delete this file (setup_table.php) after setup</li>";
    echo "</ul>";
    
} else {
    echo "<p style='color: red;'>❌ Error creating table: " . $conn->error . "</p>";
    
    echo "<p><strong>Troubleshooting:</strong></p>";
    echo "<ul>";
    echo "<li>Check if database connection is working</li>";
    echo "<li>Verify database user has CREATE TABLE permissions</li>";
    echo "<li>Check database name in db_connect.php</li>";
    echo "</ul>";
}

$conn->close();
?>

<style>
body { font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; }
table { width: 100%; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>

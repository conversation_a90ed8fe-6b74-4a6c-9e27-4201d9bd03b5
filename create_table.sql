-- SQL script to create table for storing business search results
-- Table: business_search_results

CREATE TABLE IF NOT EXISTS business_search_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    search_id VARCHAR(50) NOT NULL,
    pincode VARCHAR(10) NOT NULL,
    keywords TEXT,
    business_name VARCHAR(255) NOT NULL,
    business_address TEXT,
    business_rating DECIMAL(2,1) DEFAULT NULL,
    business_phone VARCHAR(50),
    business_types TEXT,
    place_id VARCHAR(255) UNIQUE NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    search_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_pincode (pincode),
    INDEX idx_search_id (search_id),
    INDEX idx_place_id (place_id),
    INDEX idx_search_timestamp (search_timestamp)
);

-- Table: search_history
-- To track search queries and their parameters

CREATE TABLE IF NOT EXISTS search_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    search_id VARCHAR(50) UNIQUE NOT NULL,
    pincode VARCHAR(10) NOT NULL,
    keywords TEXT,
    total_results INT DEFAULT 0,
    search_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    INDEX idx_pincode_history (pincode),
    INDEX idx_search_timestamp_history (search_timestamp)
);

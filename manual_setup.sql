-- Manual Database Setup for Business Search Application
-- Run these commands in your MySQL database: u111133901_test_alm

USE u111133901_test_alm;

-- Drop tables if they exist (optional - only if you want to recreate)
-- DROP TABLE IF EXISTS business_search_results;
-- DROP TABLE IF EXISTS search_history;

-- Create search_history table
CREATE TABLE IF NOT EXISTS search_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    search_id VARCHAR(50) UNIQUE NOT NULL,
    pincode VARCHAR(10) NOT NULL,
    keywords TEXT,
    total_results INT DEFAULT 0,
    search_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    INDEX idx_pincode_history (pincode),
    INDEX idx_search_timestamp_history (search_timestamp)
);

-- Create business_search_results table
CREATE TABLE IF NOT EXISTS business_search_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    search_id VARCHAR(50) NOT NULL,
    pincode VA<PERSON>HAR(10) NOT NULL,
    keywords TEXT,
    business_name VA<PERSON>HAR(255) NOT NULL,
    business_address TEXT,
    business_rating DECIMAL(2,1) DEFAULT NULL,
    business_phone VARCHAR(50),
    business_types TEXT,
    place_id VARCHAR(255) UNIQUE NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    search_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_pincode (pincode),
    INDEX idx_search_id (search_id),
    INDEX idx_place_id (place_id),
    INDEX idx_search_timestamp (search_timestamp),
    FOREIGN KEY (search_id) REFERENCES search_history(search_id) ON DELETE CASCADE
);

-- Verify tables were created
SHOW TABLES;

-- Check table structures
DESCRIBE search_history;
DESCRIBE business_search_results;

-- Sample queries to test after data is inserted:
-- SELECT * FROM search_history ORDER BY search_timestamp DESC LIMIT 5;
-- SELECT * FROM business_search_results ORDER BY created_at DESC LIMIT 5;
-- SELECT pincode, COUNT(*) as searches FROM search_history GROUP BY pincode ORDER BY searches DESC;

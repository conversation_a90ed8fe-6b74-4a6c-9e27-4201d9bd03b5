<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_logged_in']) || $_SESSION['user_logged_in'] !== true) {
    header("Location: login.php?redirect=view-results.php");
    exit;
}

$pageTitle = "View Search Results - Business Finder";

// Database connection
try {
    require 'db_connect.php';
    $dbConnected = true;
} catch (Exception $e) {
    $dbConnected = false;
    $dbError = $e->getMessage();
}

// Pagination settings
$recordsPerPage = 20;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $recordsPerPage;

// Filter settings
$searchFilter = isset($_GET['search']) ? trim($_GET['search']) : '';
$pincodeFilter = isset($_GET['pincode']) ? trim($_GET['pincode']) : '';
$keywordFilter = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';

// Build WHERE clause
$whereConditions = [];
$params = [];
$types = '';

if (!empty($searchFilter)) {
    $whereConditions[] = "(business_name LIKE ? OR business_address LIKE ?)";
    $params[] = "%$searchFilter%";
    $params[] = "%$searchFilter%";
    $types .= 'ss';
}

if (!empty($pincodeFilter)) {
    $whereConditions[] = "pincode = ?";
    $params[] = $pincodeFilter;
    $types .= 's';
}

if (!empty($keywordFilter)) {
    $whereConditions[] = "(keywords LIKE ? OR business_types LIKE ?)";
    $params[] = "%$keywordFilter%";
    $params[] = "%$keywordFilter%";
    $types .= 'ss';
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

include 'header.php';
?>

<style>
    .results-container {
        max-width: 1400px;
        margin: 0 auto;
    }
    
    .page-header {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .page-header h1 {
        color: #333;
        margin-bottom: 15px;
    }
    
    .filters-section {
        background: white;
        padding: 25px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .filters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .filter-group {
        display: flex;
        flex-direction: column;
    }
    
    .filter-group label {
        font-weight: 500;
        color: #333;
        margin-bottom: 5px;
    }
    
    .filter-group input {
        padding: 10px;
        border: 2px solid #ddd;
        border-radius: 5px;
        font-size: 14px;
    }
    
    .filter-group input:focus {
        outline: none;
        border-color: #667eea;
    }
    
    .filter-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }
    
    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        font-weight: 500;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        transition: transform 0.2s;
    }
    
    .btn:hover {
        transform: translateY(-2px);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
    }
    
    .results-table-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 30px;
    }
    
    .results-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .results-table th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 10px;
        text-align: left;
        font-weight: 500;
        font-size: 14px;
    }
    
    .results-table td {
        padding: 12px 10px;
        border-bottom: 1px solid #eee;
        vertical-align: top;
        font-size: 14px;
    }
    
    .results-table tr:hover {
        background-color: #f8f9fa;
    }
    
    .pagination {
        display: flex;
        justify-content: center;
        gap: 10px;
        margin-top: 30px;
    }
    
    .pagination a, .pagination span {
        padding: 10px 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
        text-decoration: none;
        color: #333;
    }
    
    .pagination a:hover {
        background: #f8f9fa;
    }
    
    .pagination .current {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }
    
    .no-results {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }
    
    .stats-bar {
        background: #f8f9fa;
        padding: 15px 25px;
        border-radius: 8px;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 15px;
    }
    
    .export-section {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-top: 30px;
        text-align: center;
    }
</style>

<div class="results-container">
    <div class="page-header">
        <h1>📋 View Search Results</h1>
        <p>Browse and manage all saved business search results from your database</p>
    </div>
    
    <?php if (!$dbConnected): ?>
        <div class="no-results">
            <h3>❌ Database Connection Error</h3>
            <p>Unable to connect to the database: <?= htmlspecialchars($dbError ?? 'Unknown error') ?></p>
            <a href="dashboard.php" class="btn btn-primary">Back to Dashboard</a>
        </div>
    <?php else: ?>
        
        <!-- Filters Section -->
        <div class="filters-section">
            <h3 style="margin-bottom: 20px;">🔍 Filter Results</h3>
            <form method="GET" action="">
                <div class="filters-grid">
                    <div class="filter-group">
                        <label for="search">Business Name/Address:</label>
                        <input type="text" id="search" name="search" value="<?= htmlspecialchars($searchFilter) ?>" placeholder="Search businesses...">
                    </div>
                    <div class="filter-group">
                        <label for="pincode">Pincode:</label>
                        <input type="text" id="pincode" name="pincode" value="<?= htmlspecialchars($pincodeFilter) ?>" placeholder="e.g., 110001">
                    </div>
                    <div class="filter-group">
                        <label for="keyword">Keywords/Types:</label>
                        <input type="text" id="keyword" name="keyword" value="<?= htmlspecialchars($keywordFilter) ?>" placeholder="e.g., restaurant">
                    </div>
                </div>
                <div class="filter-buttons">
                    <button type="submit" class="btn btn-primary">🔍 Apply Filters</button>
                    <a href="view-results.php" class="btn btn-secondary">🔄 Clear Filters</a>
                    <a href="search.php" class="btn btn-primary">➕ New Search</a>
                </div>
            </form>
        </div>
        
        <?php
        // Get total count for pagination
        $countQuery = "SELECT COUNT(*) as total FROM business_search_results $whereClause";
        if (!empty($params)) {
            $countStmt = $conn->prepare($countQuery);
            $countStmt->bind_param($types, ...$params);
            $countStmt->execute();
            $totalRecords = $countStmt->get_result()->fetch_assoc()['total'];
        } else {
            $totalRecords = $conn->query($countQuery)->fetch_assoc()['total'];
        }
        
        $totalPages = ceil($totalRecords / $recordsPerPage);
        
        // Get results for current page
        $query = "SELECT * FROM business_search_results $whereClause ORDER BY created_at DESC LIMIT $recordsPerPage OFFSET $offset";
        
        if (!empty($params)) {
            $stmt = $conn->prepare($query);
            $stmt->bind_param($types, ...$params);
            $stmt->execute();
            $result = $stmt->get_result();
        } else {
            $result = $conn->query($query);
        }
        ?>
        
        <!-- Stats Bar -->
        <div class="stats-bar">
            <div>
                <strong>📊 Total Records:</strong> <?= number_format($totalRecords) ?> businesses
                <?php if ($totalPages > 1): ?>
                    | <strong>📄 Page:</strong> <?= $page ?> of <?= $totalPages ?>
                <?php endif; ?>
            </div>
            <div>
                <strong>🔍 Showing:</strong> <?= min($recordsPerPage, $totalRecords - $offset) ?> results
            </div>
        </div>
        
        <?php if ($totalRecords > 0): ?>
            <div class="results-table-container">
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>🏢 Business Name</th>
                            <th>📍 Address</th>
                            <th>📞 Phone</th>
                            <th>⭐ Rating</th>
                            <th>🏷️ Types</th>
                            <th>📮 Pincode</th>
                            <th>🔍 Keywords</th>
                            <th>📅 Found Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($row = $result->fetch_assoc()): ?>
                            <tr>
                                <td><strong><?= htmlspecialchars($row['business_name']) ?></strong></td>
                                <td><?= htmlspecialchars($row['business_address']) ?></td>
                                <td><?= $row['business_phone'] ?: 'Not available' ?></td>
                                <td><?= $row['business_rating'] ? '⭐ ' . $row['business_rating'] : 'No rating' ?></td>
                                <td><?= htmlspecialchars($row['business_types']) ?></td>
                                <td><?= htmlspecialchars($row['pincode']) ?></td>
                                <td><?= htmlspecialchars($row['keywords'] ?: 'All types') ?></td>
                                <td><?= date('M j, Y', strtotime($row['created_at'])) ?></td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($searchFilter) ?>&pincode=<?= urlencode($pincodeFilter) ?>&keyword=<?= urlencode($keywordFilter) ?>">« Previous</a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <?php if ($i == $page): ?>
                            <span class="current"><?= $i ?></span>
                        <?php else: ?>
                            <a href="?page=<?= $i ?>&search=<?= urlencode($searchFilter) ?>&pincode=<?= urlencode($pincodeFilter) ?>&keyword=<?= urlencode($keywordFilter) ?>"><?= $i ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                        <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($searchFilter) ?>&pincode=<?= urlencode($pincodeFilter) ?>&keyword=<?= urlencode($keywordFilter) ?>">Next »</a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
        <?php else: ?>
            <div class="no-results">
                <h3>🔍 No Results Found</h3>
                <p>No business records match your current filters.</p>
                <div style="margin-top: 20px;">
                    <a href="view-results.php" class="btn btn-secondary">Clear Filters</a>
                    <a href="search.php" class="btn btn-primary">Start New Search</a>
                </div>
            </div>
        <?php endif; ?>
        
        <div class="export-section">
            <h3>📤 Export Options</h3>
            <p style="margin-bottom: 20px;">Export functionality coming soon! You can currently copy data from the table above.</p>
            <a href="admin.php" class="btn btn-primary">📊 View Analytics</a>
        </div>
        
    <?php endif; ?>
</div>

<?php include 'footer.php'; ?>
